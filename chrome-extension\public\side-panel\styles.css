/* Font imports */
@font-face {
  font-family: 'Poppins';
  font-weight: 400;
  src: url('../fonts/Poppins-400.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Poppins';
  font-weight: 600;
  src: url('../fonts/Poppins-600.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Poppins';
  font-weight: 700;
  src: url('../fonts/Poppins-700.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'DM Sans';
  font-weight: 600;
  src: url('../fonts/DM-Sans-600.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'DM Sans';
  font-weight: 800;
  src: url('../fonts/DM-Sans-800.woff2') format('woff2');
  font-display: swap;
}

/* CSS Variables */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: #dbeafe;
  --secondary-color: #6b7280;
  --secondary-light: #f3f4f6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Sidebar Container */
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

/* Sidebar edge effect */
.sidebar-container::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(180deg, 
    transparent 0%, 
    var(--border-light) 10%, 
    var(--border-medium) 50%, 
    var(--border-light) 90%, 
    transparent 100%
  );
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
}

/* Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  min-height: 64px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  border-radius: var(--radius-lg);
  color: white;
  box-shadow: var(--shadow-md);
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-title {
  font-family: 'DM Sans', sans-serif;
  font-weight: 800;
  font-size: 16px;
  color: var(--text-primary);
  line-height: 1.2;
}

.logo-subtitle {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 400;
}

.menu-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.menu-toggle:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: 20px 16px;
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-section {
  margin-bottom: 32px;
}

.nav-title {
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
  padding: 0 12px;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  gap: 12px;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
}

.nav-button:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.nav-item.active .nav-button {
  background: var(--primary-light);
  color: var(--primary-color);
  font-weight: 600;
}

.nav-icon {
  flex-shrink: 0;
  transition: transform var(--transition-fast);
}

.nav-button:hover .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  flex: 1;
  text-align: left;
}

.nav-indicator {
  position: absolute;
  right: 8px;
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.nav-item.active .nav-indicator {
  opacity: 1;
}

/* Content */
.sidebar-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: var(--bg-secondary);
}

.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-header {
  margin-bottom: 24px;
}

.section-title {
  font-family: 'DM Sans', sans-serif;
  font-weight: 800;
  font-size: 20px;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.section-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Form Elements */
.task-form {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-sm);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-size: 13px;
}

.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-light);
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  resize: vertical;
  min-height: 100px;
  transition: border-color var(--transition-fast);
}

.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-textarea::placeholder {
  color: var(--text-tertiary);
}

.form-actions {
  display: flex;
  gap: 12px;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-medium);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Task Status */
.task-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.status-indicator {
  width: 12px;
  height: 12px;
  background: var(--secondary-color);
  border-radius: 50%;
  position: relative;
}

.status-indicator.active {
  background: var(--success-color);
}

.status-indicator.active::after {
  content: '';
  position: absolute;
  inset: -4px;
  border: 2px solid var(--success-color);
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

.status-text {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

/* Export Options */
.export-options {
  display: grid;
  gap: 16px;
}

.export-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.export-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.export-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  color: var(--primary-color);
  flex-shrink: 0;
}

.export-content h4 {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
  font-size: 15px;
}

.export-content p {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Footer */
.sidebar-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-primary);
}

.settings-button {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.settings-button:hover {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

/* Scrollbar Styling */
.sidebar-nav::-webkit-scrollbar,
.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track,
.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb,
.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover,
.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-medium);
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .sidebar-header {
    padding: 12px 16px;
  }
  
  .logo-title {
    font-size: 14px;
  }
  
  .sidebar-nav {
    padding: 16px 12px;
  }
  
  .sidebar-content {
    padding: 16px;
  }
  
  .nav-button {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Focus styles for accessibility */
.nav-button:focus,
.btn:focus,
.form-textarea:focus,
.settings-button:focus,
.menu-toggle:focus,
.export-card:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-light: #666666;
    --border-medium: #333333;
    --text-secondary: #333333;
  }
} 