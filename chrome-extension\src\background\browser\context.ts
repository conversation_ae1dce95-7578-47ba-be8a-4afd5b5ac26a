import 'webextension-polyfill';
import 'webextension-polyfill';
import {
  type BrowserContextConfig,
  type BrowserState,
  DEFAULT_BROWSER_CONTEXT_CONFIG,
  type TabInfo,
  URLNotAllowedError,
} from './views';
import Page, { build_initial_state } from './page';
import { createLogger } from '@src/background/log';
import { isUrlAllowed } from './util';

const logger = createLogger('BrowserContext');
export default class BrowserContext {
  private _config: BrowserContextConfig;
  private _currentTabId: number | null = null;
  private _attachedPages: Map<number, Page> = new Map();

  constructor(config: Partial<BrowserContextConfig>) {
    this._config = { ...DEFAULT_BROWSER_CONTEXT_CONFIG, ...config };
  }

  public getConfig(): BrowserContextConfig {
    return this._config;
  }

  public updateConfig(config: Partial<BrowserContextConfig>): void {
    this._config = { ...this._config, ...config };
  }

  public updateCurrentTabId(tabId: number): void {
    // only update tab id, but don't attach it.
    this._currentTabId = tabId;
  }

  private async _getOrCreatePage(tab: chrome.tabs.Tab, forceUpdate = false): Promise<Page> {
    if (!tab.id) {
      throw new Error('Tab ID is not available');
    }

    const existingPage = this._attachedPages.get(tab.id);
    if (existingPage) {
      logger.info('getOrCreatePage', tab.id, 'already attached');
      if (!forceUpdate) {
        return existingPage;
      }
      // detach the page and remove it from the attached pages if forceUpdate is true
      await existingPage.detachPuppeteer();
      this._attachedPages.delete(tab.id);
    }
    logger.info('getOrCreatePage', tab.id, 'creating new page');
    return new Page(tab.id, tab.url || '', tab.title || '', this._config);
  }

  public async cleanup(): Promise<void> {
    const currentPage = await this.getCurrentPage();
    currentPage?.removeHighlight();
    // detach all pages
    for (const page of this._attachedPages.values()) {
      await page.detachPuppeteer();
    }
    this._attachedPages.clear();
    this._currentTabId = null;
  }

  public async attachPage(page: Page): Promise<boolean> {
    // check if page is already attached
    if (this._attachedPages.has(page.tabId)) {
      logger.info('attachPage', page.tabId, 'already attached');
      return true;
    }

    if (await page.attachPuppeteer()) {
      logger.info('attachPage', page.tabId, 'attached');
      // add page to managed pages
      this._attachedPages.set(page.tabId, page);
      return true;
    }
    return false;
  }

  public async detachPage(tabId: number): Promise<void> {
    // detach page
    const page = this._attachedPages.get(tabId);
    if (page) {
      await page.detachPuppeteer();
      // remove page from managed pages
      this._attachedPages.delete(tabId);
    }
  }

  public async getCurrentPage(): Promise<Page> {
    // 1. If _currentTabId not set, query the active tab and attach it
    if (!this._currentTabId) {
      let activeTab: chrome.tabs.Tab;
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tab?.id) {
        // open a new tab with blank page
        const newTab = await chrome.tabs.create({ url: this._config.homePageUrl });
        if (!newTab.id) {
          // this should rarely happen
          throw new Error('No tab ID available');
        }
        activeTab = newTab;
      } else {
        activeTab = tab;
      }
      logger.info('active tab', activeTab.id, activeTab.url, activeTab.title);
      const page = await this._getOrCreatePage(activeTab);
      await this.attachPage(page);
      this._currentTabId = activeTab.id || null;
      return page;
    }

    // 2. If _currentTabId is set but not in attachedPages, attach the tab
    const existingPage = this._attachedPages.get(this._currentTabId);
    if (!existingPage) {
      const tab = await chrome.tabs.get(this._currentTabId);
      const page = await this._getOrCreatePage(tab);
      // set current tab id to null if the page is not attached successfully
      await this.attachPage(page);
      return page;
    }

    // 3. Return existing page from attachedPages
    return existingPage;
  }

  /**
   * Get all tab IDs from the browser and the current window.
   * @returns A set of tab IDs.
   */
  public async getAllTabIds(): Promise<Set<number>> {
    const tabs = await chrome.tabs.query({ currentWindow: true });
    return new Set(tabs.map(tab => tab.id).filter(id => id !== undefined));
  }

  /**
   * Wait for tab events to occur after a tab is created or updated.
   * @param tabId - The ID of the tab to wait for events on.
   * @param options - An object containing options for the wait.
   * @returns A promise that resolves when the tab events occur.
   */
  private async waitForTabEvents(
    tabId: number,
    options: {
      waitForUpdate?: boolean;
      waitForActivation?: boolean;
      timeoutMs?: number;
    } = {},
  ): Promise<void> {
    const { waitForUpdate = true, waitForActivation = true, timeoutMs = 5000 } = options;

    const promises: Promise<void>[] = [];

    if (waitForUpdate) {
      const updatePromise = new Promise<void>(resolve => {
        let hasUrl = false;
        let hasTitle = false;
        let isComplete = false;

        const onUpdatedHandler = (updatedTabId: number, changeInfo: chrome.tabs.TabChangeInfo) => {
          if (updatedTabId !== tabId) return;

          if (changeInfo.url) hasUrl = true;
          if (changeInfo.title) hasTitle = true;
          if (changeInfo.status === 'complete') isComplete = true;

          // Resolve when we have all the information we need
          if (hasUrl && hasTitle && isComplete) {
            chrome.tabs.onUpdated.removeListener(onUpdatedHandler);
            resolve();
          }
        };
        chrome.tabs.onUpdated.addListener(onUpdatedHandler);

        // Check current state
        chrome.tabs.get(tabId).then(tab => {
          if (tab.url) hasUrl = true;
          if (tab.title) hasTitle = true;
          if (tab.status === 'complete') isComplete = true;

          if (hasUrl && hasTitle && isComplete) {
            chrome.tabs.onUpdated.removeListener(onUpdatedHandler);
            resolve();
          }
        });
      });
      promises.push(updatePromise);
    }

    if (waitForActivation) {
      const activatedPromise = new Promise<void>(resolve => {
        const onActivatedHandler = (activeInfo: chrome.tabs.TabActiveInfo) => {
          if (activeInfo.tabId === tabId) {
            chrome.tabs.onActivated.removeListener(onActivatedHandler);
            resolve();
          }
        };
        chrome.tabs.onActivated.addListener(onActivatedHandler);

        // Check current state
        chrome.tabs.get(tabId).then(tab => {
          if (tab.active) {
            chrome.tabs.onActivated.removeListener(onActivatedHandler);
            resolve();
          }
        });
      });
      promises.push(activatedPromise);
    }

    const timeoutPromise = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(`Tab operation timed out after ${timeoutMs} ms`)), timeoutMs),
    );

    await Promise.race([Promise.all(promises), timeoutPromise]);
  }

  public async switchTab(tabId: number): Promise<Page> {
    logger.info('switchTab', tabId);

    await chrome.tabs.update(tabId, { active: true });
    await this.waitForTabEvents(tabId, { waitForUpdate: false });

    const page = await this._getOrCreatePage(await chrome.tabs.get(tabId));
    await this.attachPage(page);
    this._currentTabId = tabId;
    return page;
  }

  public async navigateTo(url: string): Promise<void> {
    if (!isUrlAllowed(url, this._config.allowedUrls, this._config.deniedUrls)) {
      throw new URLNotAllowedError(`URL: ${url} is not allowed`);
    }

    const page = await this.getCurrentPage();
    if (!page) {
      await this.openTab(url);
      return;
    }
    // if page is attached, use puppeteer to navigate to the url
    if (page.attached) {
      await page.navigateTo(url);
      return;
    }
    //  Use chrome.tabs.update only if the page is not attached
    const tabId = page.tabId;
    // Update tab and wait for events
    await chrome.tabs.update(tabId, { url, active: true });
    await this.waitForTabEvents(tabId);

    // Reattach the page after navigation completes
    const updatedPage = await this._getOrCreatePage(await chrome.tabs.get(tabId), true);
    await this.attachPage(updatedPage);
    this._currentTabId = tabId;
  }

  public async openTab(url: string): Promise<Page> {
    if (!isUrlAllowed(url, this._config.allowedUrls, this._config.deniedUrls)) {
      throw new URLNotAllowedError(`Open tab failed. URL: ${url} is not allowed`);
    }

    // Create the new tab
    const tab = await chrome.tabs.create({ url, active: true });
    if (!tab.id) {
      throw new Error('No tab ID available');
    }
    // Wait for tab events
    await this.waitForTabEvents(tab.id);

    // Get updated tab information
    const updatedTab = await chrome.tabs.get(tab.id);
    // Create and attach the page after tab is fully loaded and activated
    const page = await this._getOrCreatePage(updatedTab);
    await this.attachPage(page);
    this._currentTabId = tab.id;

    return page;
  }

  public async closeTab(tabId: number): Promise<void> {
    await this.detachPage(tabId);
    await chrome.tabs.remove(tabId);
    // update current tab id if needed
    if (this._currentTabId === tabId) {
      this._currentTabId = null;
    }
  }

  /**
   * Remove a tab from the attached pages map. This will not run detachPuppeteer.
   * @param tabId - The ID of the tab to remove.
   */
  public removeAttachedPage(tabId: number): void {
    this._attachedPages.delete(tabId);
    // update current tab id if needed
    if (this._currentTabId === tabId) {
      this._currentTabId = null;
    }
  }

  public async getTabInfos(): Promise<TabInfo[]> {
    const tabs = await chrome.tabs.query({});
    const tabInfos: TabInfo[] = [];

    for (const tab of tabs) {
      if (tab.id && tab.url && tab.title) {
        tabInfos.push({
          id: tab.id,
          url: tab.url,
          title: tab.title,
        });
      }
    }
    return tabInfos;
  }

  public async getCachedState(useVision = false, cacheClickableElementsHashes = false): Promise<BrowserState> {
    const currentPage = await this.getCurrentPage();

    let pageState = !currentPage ? build_initial_state() : currentPage.getCachedState();
    if (!pageState) {
      pageState = await currentPage.getState(useVision, cacheClickableElementsHashes);
    }

    const tabInfos = await this.getTabInfos();
    const browserState: BrowserState = {
      ...pageState,
      tabs: tabInfos,
    };
    return browserState;
  }

  public async getState(useVision = false, cacheClickableElementsHashes = false): Promise<BrowserState> {
    const currentPage = await this.getCurrentPage();

    const pageState = !currentPage
      ? build_initial_state()
      : await currentPage.getState(useVision, cacheClickableElementsHashes);
    const tabInfos = await this.getTabInfos();
    const browserState: BrowserState = {
      ...pageState,
      tabs: tabInfos,
      // browser_errors: [],
    };
    return browserState;
  }

  public async removeHighlight(): Promise<void> {
    const page = await this.getCurrentPage();
    if (page) {
      await page.removeHighlight();
    }
  }

  /**
   * Run comprehensive DOM tree diagnostics for debugging
   */
  public async runDomTreeDiagnostics(tabId: number): Promise<any> {
    try {
      const tab = await chrome.tabs.get(tabId);
      if (!tab || !tab.url) {
        return {
          error: 'Tab not found or has no URL',
          tabId: tabId
        };
      }

      logger.info(`Running DOM tree diagnostics for tab ${tabId}: ${tab.url}`);

      // Test 1: Basic tab and page accessibility
      const basicCheck = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => ({
          url: window.location.href,
          title: document.title,
          readyState: document.readyState,
          hasBody: !!document.body,
          hasHead: !!document.head,
          hasDocumentElement: !!document.documentElement,
          bodyChildren: document.body ? document.body.children.length : 0,
          contentType: document.contentType || 'unknown',
          characterSet: document.characterSet || 'unknown',
          securityOrigin: window.location.origin,
          userAgent: navigator.userAgent,
          viewport: {
            width: window.innerWidth,
            height: window.innerHeight,
            scrollX: window.scrollX,
            scrollY: window.scrollY
          }
        })
      });

      // Test 2: Check script injection status
      const injectionStatus = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => ({
          hasBuildDomTree: typeof window.buildDomTree === 'function',
          hasTurn2Markdown: typeof window.turn2Markdown === 'function',
          hasParserReadability: typeof window.parserReadability === 'function',
          windowBuildDomTreeType: typeof (window as any).buildDomTree,
          globalWindowKeys: Object.keys(window).filter(key => 
            key.includes('buildDom') || 
            key.includes('turn2') || 
            key.includes('parser') ||
            key.includes('nano')
          )
        })
      });

      // Test 3: Try to inject and verify buildDomTree
      let injectionResult: any = { success: false };
      if (!injectionStatus[0]?.result?.hasBuildDomTree) {
        try {
          await chrome.scripting.executeScript({
            target: { tabId },
            files: ['buildDomTree.js'],
          });

          const verifyInjection = await chrome.scripting.executeScript({
            target: { tabId },
            func: () => ({
              hasBuildDomTree: typeof window.buildDomTree === 'function',
              injectionSuccessful: true,
              buildDomTreeLength: window.buildDomTree ? window.buildDomTree.toString().length : 0
            })
          });

          injectionResult = { 
            success: true, 
            result: verifyInjection[0]?.result 
          };
        } catch (error) {
          injectionResult = {
            success: false,
            error: error instanceof Error ? error.message : String(error)
          };
        }
      } else {
        injectionResult = { success: true, alreadyInjected: true };
      }

      // Test 4: Try to run buildDomTree with minimal args
      let buildDomTreeTest: any = { success: false };
      try {
        const testResult = await chrome.scripting.executeScript({
          target: { tabId },
          func: (args) => {
            try {
              if (typeof window.buildDomTree !== 'function') {
                return { error: 'buildDomTree function not available' };
              }

              const startTime = performance.now();
              const result = window.buildDomTree(args);
              const endTime = performance.now();

              return {
                success: true,
                executionTime: endTime - startTime,
                hasResult: !!result,
                resultType: typeof result,
                hasMap: !!(result && (result as any).map),
                hasRootId: !!(result && (result as any).rootId),
                mapSize: result && (result as any).map ? Object.keys((result as any).map).length : 0,
                rootId: result && (result as any).rootId ? (result as any).rootId : 'none'
              };
            } catch (error) {
              return {
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined
              };
            }
          },
          args: [{
            showHighlightElements: false,
            focusHighlightIndex: -1,
            viewportExpansion: 0,
            debugMode: true
          }]
        });

        buildDomTreeTest = { 
          success: true, 
          result: testResult[0]?.result 
        };
      } catch (error) {
        buildDomTreeTest = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }

      // Test 5: Check for security policy violations and JavaScript errors
      const securityTest = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          const violations: any[] = [];
          const errors: any[] = [];

          // Listen for CSP violations
          if (window.addEventListener) {
            window.addEventListener('securitypolicyviolation', (e) => {
              violations.push({
                violatedDirective: e.violatedDirective,
                sourceFile: e.sourceFile,
                lineNumber: e.lineNumber,
                columnNumber: e.columnNumber,
                blockedURI: e.blockedURI
              });
            });
          }

          // Check for existing errors
          const originalError = window.onerror;
          window.onerror = function(message, source, lineno, colno, error) {
            errors.push({
              message: message,
              source: source,
              line: lineno,
              column: colno,
              error: error ? error.toString() : 'none'
            });
            if (originalError) originalError.call(this, message, source, lineno, colno, error);
            return false;
          };

          return {
            cspViolations: violations,
            jsErrors: errors,
            hasConsole: typeof console !== 'undefined',
            consoleEnabled: true
          };
        }
      });

      const diagnosticsResult = {
        tabId: tabId,
        url: tab.url,
        timestamp: new Date().toISOString(),
        basicCheck: basicCheck[0]?.result,
        injectionStatus: injectionStatus[0]?.result,
        injectionResult: injectionResult,
        buildDomTreeTest: buildDomTreeTest,
        securityTest: securityTest[0]?.result,
        summary: {
          tabAccessible: !!basicCheck[0]?.result,
          scriptInjectable: injectionResult.success,
          buildDomTreeWorking: buildDomTreeTest.success && buildDomTreeTest.result?.success,
          hasSecurityIssues: !!(securityTest[0]?.result?.cspViolations?.length || securityTest[0]?.result?.jsErrors?.length)
        }
      };

      logger.info('DOM tree diagnostics completed:', diagnosticsResult.summary);
      return diagnosticsResult;

    } catch (error) {
      logger.error('DOM tree diagnostics failed:', error);
      return {
        error: error instanceof Error ? error.message : String(error),
        tabId: tabId,
        timestamp: new Date().toISOString()
      };
    }
  }
}
