#!/usr/bin/env node

/**
 * Build Verification Script for Nanobrowser Chrome Extension
 * Verifies that all essential files are present and properly configured
 */

import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const distDir = path.resolve(__dirname, '..', 'dist');

// ANSI colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`${colors.bold}${colors.blue}${msg}${colors.reset}`)
};

// Required files for extension to work
const requiredFiles = [
  'manifest.json',
  'background.iife.js',
  'content/index.iife.js',
  'lib/xlsx.full.min.js',
  'lib/apexcharts.min.js', 
  'lib/pdf-lib.min.js',
  'icon-32.svg',
  'icon-128.svg',
  '_locales/en/messages.json'
];

// Optional files (side-panel and options need to be built separately)
const optionalFiles = [
  'side-panel/index.html',
  'options/index.html'
];

function checkFile(filePath) {
  const fullPath = path.join(distDir, filePath);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    const stats = fs.statSync(fullPath);
    const size = (stats.size / 1024).toFixed(2);
    log.success(`${filePath} (${size} KB)`);
    return true;
  } else {
    log.error(`Missing: ${filePath}`);
    return false;
  }
}

function verifyManifest() {
  const manifestPath = path.join(distDir, 'manifest.json');
  
  if (!fs.existsSync(manifestPath)) {
    log.error('manifest.json not found');
    return false;
  }
  
  try {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    
    // Check essential manifest properties
    const requiredProps = ['manifest_version', 'name', 'version', 'permissions'];
    const missingProps = requiredProps.filter(prop => !manifest[prop]);
    
    if (missingProps.length > 0) {
      log.error(`Manifest missing required properties: ${missingProps.join(', ')}`);
      return false;
    }
    
    // Check specific requirements
    if (manifest.manifest_version !== 3) {
      log.error('Manifest version must be 3');
      return false;
    }
    
    if (!manifest.permissions.includes('storage')) {
      log.warning('Storage permission not found in manifest');
    }
    
    if (!manifest.background || !manifest.background.service_worker) {
      log.error('Background service worker not configured');
      return false;
    }
    
    log.success(`Manifest v${manifest.manifest_version} valid (${manifest.name} v${manifest.version})`);
    return true;
    
  } catch (error) {
    log.error(`Invalid manifest.json: ${error.message}`);
    return false;
  }
}

function main() {
  console.log('');
  log.header('🔍 Nanobrowser Chrome Extension Build Verification');
  console.log('');
  
  // Check if dist directory exists
  if (!fs.existsSync(distDir)) {
    log.error(`Distribution directory not found: ${distDir}`);
    log.info('Run "npm run build" to create the dist directory');
    process.exit(1);
  }
  
  log.info(`Checking build output in: ${distDir}`);
  console.log('');
  
  // Verify manifest
  log.header('📄 Manifest Verification');
  const manifestValid = verifyManifest();
  console.log('');
  
  // Check required files
  log.header('📁 Required Files Check');
  let allRequiredPresent = true;
  
  for (const file of requiredFiles) {
    const exists = checkFile(file);
    if (!exists) allRequiredPresent = false;
  }
  console.log('');
  
  // Check optional files
  log.header('📁 Optional Files Check');
  let optionalCount = 0;
  
  for (const file of optionalFiles) {
    const exists = checkFile(file);
    if (exists) optionalCount++;
  }
  console.log('');
  
  // Summary
  log.header('📊 Build Summary');
  
  if (manifestValid && allRequiredPresent) {
    log.success('Core extension is ready for installation!');
    
    if (optionalCount === optionalFiles.length) {
      log.success('All UI components are built and ready!');
    } else {
      log.warning(`Optional UI components: ${optionalCount}/${optionalFiles.length} built`);
      log.info('To build UI components, run:');
      log.info('  cd ../pages/side-panel && npm run build');
      log.info('  cd ../pages/options && npm run build');
    }
    
    console.log('');
    log.header('🚀 Installation Instructions');
    log.info('1. Open chrome://extensions/');
    log.info('2. Enable "Developer mode"');
    log.info('3. Click "Load unpacked"');
    log.info(`4. Select: ${distDir}`);
    
  } else {
    log.error('Build verification failed!');
    log.info('Run "npm run build" to rebuild the extension');
    process.exit(1);
  }
  
  console.log('');
}

main();