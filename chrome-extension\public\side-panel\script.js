// Side Panel Script
class SidePanelApp {
  constructor() {
    this.port = null;
    this.currentTab = null;
    this.taskStatus = 'idle';
    this.init();
  }

  async init() {
    await this.connectToBackground();
    this.setupEventListeners();
    this.setupHeartbeat();
    await this.getCurrentTab();
    this.updateUIState();
  }

  // Connection Management
  async connectToBackground() {
    try {
      this.port = chrome.runtime.connect({ name: 'side-panel-connection' });
      
      this.port.onMessage.addListener((message) => {
        this.handleBackgroundMessage(message);
      });

      this.port.onDisconnect.addListener(() => {
        console.log('Disconnected from background');
        this.port = null;
        setTimeout(() => this.connectToBackground(), 1000);
      });
      
      console.log('Connected to background script');
    } catch (error) {
      console.error('Failed to connect to background:', error);
    }
  }

  setupHeartbeat() {
    // Send heartbeat every 5 seconds to keep connection alive
    setInterval(() => {
      if (this.port) {
        this.port.postMessage({ type: 'heartbeat' });
      }
    }, 5000);
  }

  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
    } catch (error) {
      console.error('Failed to get current tab:', error);
    }
  }

  // Event Listeners
  setupEventListeners() {
    // Navigation
    document.querySelectorAll('.nav-button').forEach(button => {
      button.addEventListener('click', (e) => {
        const section = e.currentTarget.dataset.section;
        this.showSection(section);
      });
    });

    // Task Form
    const startTaskBtn = document.getElementById('startTask');
    const pauseTaskBtn = document.getElementById('pauseTask');
    const taskInput = document.getElementById('taskInput');

    if (startTaskBtn) {
      startTaskBtn.addEventListener('click', () => {
        const task = taskInput.value.trim();
        if (task) {
          this.startTask(task);
        }
      });
    }

    if (pauseTaskBtn) {
      pauseTaskBtn.addEventListener('click', () => {
        this.pauseTask();
      });
    }

    // Export cards
    document.querySelectorAll('.export-card').forEach(card => {
      card.addEventListener('click', (e) => {
        const cardText = e.currentTarget.querySelector('h4').textContent;
        this.handleExport(cardText);
      });
    });

    // Settings button
    const settingsBtn = document.querySelector('.settings-button');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSection('settings');
      });
    }

    // Tools
    const screenshotBtn = document.querySelector('[data-section="screenshot"]');
    const stateBtn = document.querySelector('[data-section="state"]');

    if (screenshotBtn) {
      screenshotBtn.addEventListener('click', () => {
        this.takeScreenshot();
      });
    }

    if (stateBtn) {
      stateBtn.addEventListener('click', () => {
        this.getPageState();
      });
    }
  }

  // Navigation
  showSection(sectionName) {
    // Update navigation
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    
    const activeNavItem = document.querySelector(`[data-section="${sectionName}"]`)?.closest('.nav-item');
    if (activeNavItem) {
      activeNavItem.classList.add('active');
    }

    // Update content
    document.querySelectorAll('.content-section').forEach(section => {
      section.classList.remove('active');
    });

    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
      targetSection.classList.add('active');
    }
  }

  // Task Management
  async startTask(task) {
    if (!this.currentTab?.id) {
      this.showNotification('No active tab found', 'error');
      return;
    }

    const taskId = `task_${Date.now()}`;
    
    try {
      this.updateTaskStatus('starting', 'Starting task...');
      
      if (this.port) {
        this.port.postMessage({
          type: 'new_task',
          task: task,
          taskId: taskId,
          tabId: this.currentTab.id
        });
      }
    } catch (error) {
      console.error('Failed to start task:', error);
      this.updateTaskStatus('error', 'Failed to start task');
    }
  }

  async pauseTask() {
    try {
      if (this.port) {
        this.port.postMessage({
          type: 'pause_task'
        });
      }
    } catch (error) {
      console.error('Failed to pause task:', error);
    }
  }

  // Tools
  async takeScreenshot() {
    if (!this.currentTab?.id) {
      this.showNotification('No active tab found', 'error');
      return;
    }

    try {
      if (this.port) {
        this.port.postMessage({
          type: 'screenshot',
          tabId: this.currentTab.id
        });
      }
    } catch (error) {
      console.error('Failed to take screenshot:', error);
    }
  }

  async getPageState() {
    try {
      if (this.port) {
        this.port.postMessage({
          type: 'state'
        });
      }
    } catch (error) {
      console.error('Failed to get page state:', error);
    }
  }

  // Export
  handleExport(exportType) {
    if (!this.currentTab?.id) {
      this.showNotification('No active tab found', 'error');
      return;
    }

    // This would integrate with your data extraction service
    console.log(`Exporting: ${exportType}`);
    this.showNotification(`Export ${exportType} initiated`, 'success');
  }

  // Message Handling
  handleBackgroundMessage(message) {
    console.log('Received message:', message);

    switch (message.type) {
      case 'heartbeat_ack':
        // Heartbeat acknowledged
        break;
        
      case 'task_started':
        this.updateTaskStatus('running', 'Task is running...');
        break;
        
      case 'task_completed':
        this.updateTaskStatus('completed', 'Task completed successfully');
        break;
        
      case 'task_paused':
        this.updateTaskStatus('paused', 'Task paused');
        break;
        
      case 'task_error':
        this.updateTaskStatus('error', message.error || 'Task failed');
        break;
        
      case 'success':
        if (message.result?.screenshot) {
          this.showNotification('Screenshot captured', 'success');
        } else {
          this.showNotification(message.message || 'Operation successful', 'success');
        }
        break;
        
      case 'error':
        this.showNotification(message.error || 'An error occurred', 'error');
        break;
        
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  // UI Updates
  updateTaskStatus(status, message) {
    this.taskStatus = status;
    
    const statusIndicator = document.querySelector('.status-indicator');
    const statusText = document.querySelector('.status-text');
    const startBtn = document.getElementById('startTask');
    const pauseBtn = document.getElementById('pauseTask');

    if (statusIndicator) {
      statusIndicator.className = 'status-indicator';
      if (status === 'running' || status === 'starting') {
        statusIndicator.classList.add('active');
      }
    }

    if (statusText) {
      statusText.textContent = message;
    }

    if (startBtn) {
      startBtn.disabled = (status === 'running' || status === 'starting');
    }

    if (pauseBtn) {
      pauseBtn.disabled = !(status === 'running');
    }
  }

  updateUIState() {
    // Update UI based on current state
    this.updateTaskStatus('idle', 'Ready to start');
  }

  // Notifications
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 16px',
      borderRadius: '8px',
      color: 'white',
      fontWeight: '500',
      fontSize: '14px',
      zIndex: '1000',
      opacity: '0',
      transform: 'translateY(-10px)',
      transition: 'all 0.3s ease'
    });

    // Set background color based on type
    const colors = {
      success: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6'
    };
    notification.style.backgroundColor = colors[type] || colors.info;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    requestAnimationFrame(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateY(0)';
    });

    // Remove after delay
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateY(-10px)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // Utility
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new SidePanelApp();
});

// Handle tab changes
chrome.tabs?.onActivated?.addListener(async (activeInfo) => {
  // Update current tab when user switches tabs
  if (window.sidePanelApp) {
    await window.sidePanelApp.getCurrentTab();
  }
});

// Export for global access
window.SidePanelApp = SidePanelApp; 