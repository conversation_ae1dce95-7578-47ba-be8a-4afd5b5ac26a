<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Nano Browser Extension</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="sidebar-container">
    <!-- Header Section -->
    <div class="sidebar-header">
      <div class="logo-section">
        <div class="logo-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2L2 7l10 5 10-5-10-5z"/>
            <path d="M2 17l10 5 10-5"/>
            <path d="M2 12l10 5 10-5"/>
          </svg>
        </div>
        <div class="logo-text">
          <span class="logo-title"><PERSON><PERSON> Browser</span>
          <span class="logo-subtitle">AI Automation</span>
        </div>
      </div>
      <button class="menu-toggle" id="menuToggle">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="3" y1="6" x2="21" y2="6"/>
          <line x1="3" y1="12" x2="21" y2="12"/>
          <line x1="3" y1="18" x2="21" y2="18"/>
        </svg>
      </button>
    </div>

    <!-- Navigation Section -->
    <nav class="sidebar-nav">
      <div class="nav-section">
        <h3 class="nav-title">Automation</h3>
        <ul class="nav-list">
          <li class="nav-item active">
            <button class="nav-button" data-section="tasks">
              <svg class="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M9 12l2 2 4-4"/>
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
                <path d="M3 12h6m6 0h6"/>
              </svg>
              <span class="nav-text">Tasks</span>
              <div class="nav-indicator"></div>
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-button" data-section="data">
              <svg class="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>
              <span class="nav-text">Data Export</span>
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-button" data-section="history">
              <svg class="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
              <span class="nav-text">History</span>
            </button>
          </li>
        </ul>
      </div>

      <div class="nav-section">
        <h3 class="nav-title">Tools</h3>
        <ul class="nav-list">
          <li class="nav-item">
            <button class="nav-button" data-section="screenshot">
              <svg class="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                <circle cx="12" cy="13" r="4"/>
              </svg>
              <span class="nav-text">Screenshot</span>
            </button>
          </li>
          <li class="nav-item">
            <button class="nav-button" data-section="state">
              <svg class="nav-icon" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M12 2v20M2 12h20"/>
                <circle cx="12" cy="12" r="9"/>
              </svg>
              <span class="nav-text">Page State</span>
            </button>
          </li>
        </ul>
      </div>
    </nav>

    <!-- Main Content Area -->
    <div class="sidebar-content">
      <!-- Task Section -->
      <div class="content-section active" id="tasks-section">
        <div class="section-header">
          <h2 class="section-title">Create Task</h2>
          <div class="section-subtitle">Automate your browser actions</div>
        </div>
        
        <div class="task-form">
          <div class="form-group">
            <label for="taskInput" class="form-label">Task Description</label>
            <textarea 
              id="taskInput" 
              class="form-textarea" 
              placeholder="Describe what you want the browser to do..."
              rows="4"
            ></textarea>
          </div>
          
          <div class="form-actions">
            <button class="btn btn-primary" id="startTask">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="5,3 19,12 5,21"/>
              </svg>
              Start Task
            </button>
            <button class="btn btn-secondary" id="pauseTask" disabled>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="6" y="4" width="4" height="16"/>
                <rect x="14" y="4" width="4" height="16"/>
              </svg>
              Pause
            </button>
          </div>
        </div>

        <div class="task-status" id="taskStatus">
          <div class="status-indicator"></div>
          <span class="status-text">Ready to start</span>
        </div>
      </div>

      <!-- Data Export Section -->
      <div class="content-section" id="data-section">
        <div class="section-header">
          <h2 class="section-title">Data Export</h2>
          <div class="section-subtitle">Extract and export page data</div>
        </div>
        
        <div class="export-options">
          <button class="export-card">
            <div class="export-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
              </svg>
            </div>
            <div class="export-content">
              <h4>Export to CSV</h4>
              <p>Extract table data and export as CSV</p>
            </div>
          </button>
          
          <button class="export-card">
            <div class="export-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
              </svg>
            </div>
            <div class="export-content">
              <h4>Generate Report</h4>
              <p>Create PDF report with findings</p>
            </div>
          </button>
        </div>
      </div>

      <!-- Other sections would go here -->
    </div>

    <!-- Footer -->
    <div class="sidebar-footer">
      <button class="settings-button" data-section="settings">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>
        <span>Settings</span>
      </button>
    </div>
  </div>

  <script src="script.js"></script>
</body>
</html> 